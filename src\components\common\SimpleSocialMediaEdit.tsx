'use client'
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch } from "@/store";
import { editCoachSocialMediaLinks, fetchCoachSocialMediaLinks } from "@/store/slices/coach/coachProfileSlice";
import { EachSocialMediaItem } from "@/utils/interfaces";
import { Check, Loader, PencilLine, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";

interface IProps {
    toggleSocialMediaSection: boolean;
    onChangeToggleSection: (checked: boolean) => void
    list: EachSocialMediaItem[];
    origin: string;
    loading?: boolean;
    fetchLoading?: boolean;
}

const SimpleSocialMediaEdit = ({
    toggleSocialMediaSection,
    onChangeToggleSection,
    list,
    origin,
    loading,
    fetchLoading,
}: IProps) => {
    const [socialMediaList, setSocialMediaList] = useState<EachSocialMediaItem[]>(list)
    const [editableIndex, setEditableIndex] = useState<number | null>(null);
    const [editingItem, setEditingItem] = useState<{link: string, isHidden: boolean} | null>(null);
    const [isUpdating, setIsUpdating] = useState(false);
    const dispatch = useDispatch<AppDispatch>()
    const { userId, roleId, isPremiumUser } = useTokenValues()
    const canEdit = origin === 'coach' || (origin === 'athlete' && isPremiumUser)

    useEffect(() => {
        if (list) {
            setSocialMediaList(list)
        }
    }, [list])

    const handleEdit = (index: number) => {
        const item = socialMediaList[index];
        setEditableIndex(index);
        setEditingItem({
            link: item.link || '',
            isHidden: !item.isEnable
        });
    };

    const handleCancel = () => {
        setEditableIndex(null);
        setEditingItem(null);
    };

    const handleLinkChange = (value: string) => {
        if (editingItem) {
            setEditingItem({
                ...editingItem,
                link: value
            });
        }
    };

    const handleVisibilityChange = (checked: boolean) => {
        if (editingItem) {
            setEditingItem({
                ...editingItem,
                isHidden: !checked
            });
        }
    };

    const handleSave = async () => {
        if (editableIndex === null || !editingItem) return;

        const item = socialMediaList[editableIndex];
        setIsUpdating(true);

        try {
            // Create payload with all social media links, updating the current one
            const updatedSocialMediaList = socialMediaList.map((socialItem, index) => {
                if (index === editableIndex) {
                    return {
                        roleId: 3, // Coach role
                        coachId: parseInt(localStorage.getItem("profileId") || "0"),
                        userId: parseInt(localStorage.getItem("userId") || "0"),
                        socialMedia: socialItem.id,
                        socialMediaLink: editingItem.link,
                        isHidden: editingItem.isHidden
                    };
                } else {
                    // Keep existing data for other platforms
                    return {
                        roleId: 3,
                        coachId: parseInt(localStorage.getItem("profileId") || "0"),
                        userId: parseInt(localStorage.getItem("userId") || "0"),
                        socialMedia: socialItem.id,
                        socialMediaLink: socialItem.link || "",
                        isHidden: !socialItem.isEnable
                    };
                }
            });

            // Filter out empty links if needed (optional)
            const payload = updatedSocialMediaList.filter(item => item.socialMediaLink.trim() !== "");

            console.log('Saving payload:', payload);

            const resultAction = await dispatch(editCoachSocialMediaLinks(payload));

            if (editCoachSocialMediaLinks.fulfilled.match(resultAction)) {
                // Update local state
                const updatedList = [...socialMediaList];
                updatedList[editableIndex] = {
                    ...item,
                    link: editingItem.link,
                    isEnable: !editingItem.isHidden
                };
                setSocialMediaList(updatedList);

                // Reset editing state
                setEditableIndex(null);
                setEditingItem(null);

                // Refresh data from server
                await dispatch(fetchCoachSocialMediaLinks());

                toast.success(`${item.id} link updated successfully!`);
            } else {
                toast.error('Failed to update social media link');
            }
        } catch (error) {
            console.error('Error updating social media link:', error);
            toast.error('An error occurred while updating');
        } finally {
            setIsUpdating(false);
        }
    };

    if (fetchLoading) {
        return <div className="p-4">Loading social media data...</div>;
    }

    return (
        <div className="w-full flex flex-col gap-6 bg-slate-100 p-4 rounded-lg">
            <div className="flex items-center justify-center gap-4">
                <h3 className="font-bold text-xl text-center">Social Media</h3>
                <Switch
                    checked={toggleSocialMediaSection}
                    onCheckedChange={onChangeToggleSection}
                    disabled={!canEdit}
                />
            </div>

            {toggleSocialMediaSection && (
                <div className="space-y-4">
                    {socialMediaList?.map((item, index) => (
                        <div key={`${item.id}-${index}`} className="bg-white p-4 rounded-lg border">
                            <div className="flex items-center gap-3 mb-3">
                                <img src={item.icon} alt={item.id} className="h-6 w-6" />
                                <span className="font-medium capitalize">{item.id}</span>
                                <span className={`ml-auto px-2 py-1 rounded text-xs ${
                                    item.isEnable ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                                }`}>
                                    {item.isEnable ? 'Visible' : 'Hidden'}
                                </span>
                            </div>

                            {editableIndex === index ? (
                                <div className="space-y-3">
                                    <Input
                                        value={editingItem?.link || ''}
                                        onChange={(e) => handleLinkChange(e.target.value)}
                                        placeholder={`Enter ${item.id} URL`}
                                        disabled={isUpdating}
                                    />
                                    
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm">Visible:</span>
                                        <Switch
                                            checked={!editingItem?.isHidden}
                                            onCheckedChange={handleVisibilityChange}
                                            disabled={isUpdating}
                                        />
                                        <span className="text-sm text-gray-500">
                                            {editingItem?.isHidden ? 'Hidden' : 'Visible'}
                                        </span>
                                    </div>

                                    <div className="flex gap-2">
                                        <Button
                                            size="sm"
                                            onClick={handleSave}
                                            disabled={isUpdating}
                                            className="bg-green-600 hover:bg-green-700"
                                        >
                                            {isUpdating ? <Loader className="w-4 h-4 animate-spin" /> : <Check className="w-4 h-4" />}
                                        </Button>
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={handleCancel}
                                            disabled={isUpdating}
                                        >
                                            <X className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <div className="flex-1">
                                            {item.link ? (
                                                <a
                                                    href={item.link}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-blue-600 hover:underline break-all"
                                                >
                                                    {item.link}
                                                </a>
                                            ) : (
                                                <span className="text-gray-400 italic">No link added</span>
                                            )}
                                        </div>
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleEdit(index)}
                                            disabled={!canEdit}
                                        >
                                            <PencilLine className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            )}

            {/* Debug Info */}
            <div className="text-xs text-gray-500 mt-4">
                <p>Can Edit: {canEdit ? 'Yes' : 'No'}</p>
                <p>Origin: {origin}</p>
                <p>Editable Index: {editableIndex !== null ? editableIndex : 'None'}</p>
                <p>Items Count: {socialMediaList?.length || 0}</p>
            </div>
        </div>
    );
};

export default SimpleSocialMediaEdit;
