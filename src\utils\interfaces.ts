// export type ProfileData = {
//   id: number;
//   age: number;
//   gender: string;
//   grade: string;
//   city: string;
//   residentialState: string;
//   bio: string;
//   achievements: string;
//   parentFirstName: string;
//   parentLastName: string;
//   parentEmail: string;
//   parentPhone: string;
//   parentConsent: boolean;
//   instagramLink: string;
//   twitterLink: string;
//   athleteSpecialty: string;
//   sportsId: string;
//   isApprove: string;
//   isPublic: boolean;
//   isSubscription: string;
//   isActive: boolean;
//   subscriptionId: number;
//   userId: number;
//   createdUser: number;
//   updatedUser: number;
//   referralCodeUsedId: number;
//   createdAt: string;
//   updatedAt: string;
//   deletedAt: string | null;
//   roleId: number;
// };

export type EachGalleryItem = {
  cmId: number;
  cmssectionId: number;
  createdAt: string;
  deletedAt: null | string;
  fileDescription: string;
  fileLocation: string;
  fileTitle: string;
  fileType: string;
  id: number;
  updatedAt: string;
};

export type EachSocialMediaItem = {
  id: string;
  icon: string;
  link: string;
  isEditable?: boolean;
  isEnable: boolean;
};

export type EachQuickLinkItem = {
  id: string;
  title: string;
  link: string;
  isEditable: boolean;
};

export type HomeBanners = {
  loading: boolean;
  error: string;
  homeBannerImages: EachGalleryItem[];
};

export type ProfileData = {
  id: number;
  userId: number;
  createdUser: number;
  updatedUser: number;
  subscriptionId: number;
  referralCodeUsedId: number;
  age: number;
  grade: string;
  gender: string;
  bio: string;
  achievements: string;
  school: string;
  sportsId: string;
  athleteSpecialty: string;
  city: string;
  residentialState: string;
  instagramLink: string;
  twitterLink: string;
  parentFirstName: string;
  parentLastName: string;
  parentEmail: string;
  parentPhone: string;
  parentConsent: boolean;
  isActive: boolean;
  isApprove: string;
  isPublish: boolean;
  isSubscription: string;
  updatedAt: string;
  deletedAt: string | null;
};

export type EachAthleteGalleryItem = {
  id: string;
  file?: File | undefined;
  imageTitle: string;
  isEditable: boolean;
  url: string;
};

export type EachAchievementItem = {
  id: number;
  title: string;
  date: Date | undefined | string;
  link: string;
  tags: { value: number; label: string }[];
  blurb: string;
  file?: string;
};

export type FetchedSocialMedia = {
  id: number;
  roleId: number;
  athleteId: number;
  userId: number;
  socialMedia: string;
  socialMediaLink: string;
  isHidden: boolean;
  createdAt: string;
  updatedAt: string;
};

export type CoachSocialMediaResponse = {
  id: number;
  roleId: number;
  coachId: number;
  userId: number;
  socialMedia: string;
  socialMediaLink: string;
  isHidden: boolean;
  createdAt: string;
  updatedAt: string;
};

export type EditSocialMediaPayload = {
  roleId: number;
  coachId: number;
  userId: number;
  socialMedia: string;
  socialMediaLink: string;
  isHidden: boolean;
};

export type EditSocialMediaArrayPayload = EditSocialMediaPayload[];

export type EachImageItem = {
  name: string;
  url: File | undefined;
  base64: string;
  blob: Blob;
};

export type AthleteProfileTypes = {
  apiStatus: string;
  error: string;
  profileUrl: string;
  profileToggle: boolean;
  profileCardData: {
    firstName: string;
    lastName: string;
    blurb: string;
    ageGroup: {
      value: number;
      label: string;
    };
    gender: {
      value: number;
      label: string;
    };
    profileImage?: any;
    primarySportsList?: string[];
  } | null;
  isBioEditable: boolean;
  bio: string;
  socialMediaToggle: boolean;
  quickLinksToggle: boolean;
  physicalStatsToggle: boolean;
  physStats: { hFeet: string; hInches: string; weight: string } | null;
  listOfFourPhysStats: {    
    name: string;
    unit: string;
    value: string;
  }[];
  openVirtualSession: boolean;
  athleteSocialMediaList: EachSocialMediaItem[];
  athleteQuickLinksList: EachQuickLinkItem[];
  toggleGallery: boolean;
  athleteGalleryList: ProfileGalleryItem[];
  mySportsList: EachSportItem[];
  sportProfileId: number | null;
  selectedGrowthList: Option[];
  achieveOrAccomplish: string;
  toggleAchievements: boolean;
  achievementData: EachAchievementItem | null;
  addedAchievementsList: EachAchievementItem[];
  selectedState: EachSearchItem | null;
  selectedCounties: EachSearchItem[];
  selectedLocations: EachSearchItem[];
  athleteAddedStateLocationsList: AddedStateLocationsItem[];
  athleteLearning: {
    currentSchoolName: Option;
    otherSchoolName: string;
    graduationYear: string;
    grade: string;
    overallAcademic: string;
    gpa: string;
    sat: string;
    act: string;
    progress: string;
    academicMmt: string;
  } | null;
  selectedLearingTopicsList: Option[];
  toggleContactSection: boolean;
  parentGuardianData: {
    firstName: string;
    lastName: string;
    phone: string;
    togglePhone: boolean;
    email: string;
    toggleEmail: boolean;
    relationship: "";
    toggleRelation: boolean;
    searchMyProfileCA: boolean;
    lastTermsAcceptedDt: string | null;
  } | null;
  toggleSchoolView: boolean;
  physStatsId: number | null;
  selectedAchievementId: number | null;
  isVerifyEmail: boolean;
};

export type EachSpecility = {
  id: string;
  specilityName: string;
};
export type EachSportItem = {
  id: string;
  isPrimary: boolean;
  sportName: string | null;
  sportId: number;
  sportLevel?: string;
  specilities: EachSpecility[];
  isEditable: boolean;
};

export type EachSearchItem = {
  value: number;
  label: string;
};

// export type AddedStateLocationsItem = {
//   stateId: number;
//   stateName: string;
//   countyId: number;
//   countyName: string;
//   locationIds: number[];
//   locationNames: string[];
// };

export type AddedStateLocationsItem = {
  stateId: number;
  stateName: string;
  counties: {
    countyId: number;
    countyName: string;
    cities: {
      cityId: number;
      cityName: string;
    }[];
  }[];
};

export type AddedSportLevelItem = {
  levelId: number;
  levelName: string;
};

export type AddedSpecilityItem = {
  specilityId: number;
  specilityName: string;
};

export type EachMileStoneVictoryItem = {
  id: number;
  date: Date | undefined;
  title: string;
  link: string;
  blurb: string;
  tags: { value: number; label: string }[];
  file: any;
};

export type AthleteSportProfileStates = {
  apiStatus: string;
  error: string;
  isSportProfileHide: boolean;
  sportFormData: {
    selectedSport: Option | null;
    isPrimarySport: boolean;
    yearsPlayed: string;
    currentSeason: string;
    selectedSportLevel: Option | null;
    addedSportSpecilitiesList: Option[];
    currentTeam: string;
  } | null;
  statsFormData: {
    seasonName: string;
    date: Date | string | undefined;
    statsName: Option | null;
    statsValueNumeric: string;
    statsValueText: string;
    isPublished: boolean;
  } | null;
  statsList: any[];
  toggleVideoSection: boolean;
  toggleHighlightLinks: boolean;
  isEditHighlightLinks: boolean;
  latestVideoData: {
    title: string;
    aboutVideo: string;
    video: string | null;
  } | null;
  highlightLinksList: { id: number; text: string; url: string }[];
  addedHighlightVideoList: {
    id: number;
    text: string;
    aboutVideo: string;
    video: string;
  }[];
  toggleMileStone: boolean;
  isAddMileStone: boolean;
  mileStoneData: EachMileStoneVictoryItem | null;
  addedMileStonesList: EachMileStoneVictoryItem[];
  toggleVictoryVault: boolean;
  isVictoryVault: boolean;
  vicotryVaultData: EachMileStoneVictoryItem | null;
  addedVictoryVaultList: EachMileStoneVictoryItem[];
  vaultId: number | null;
  mileStoneId: number | null;
  videoId: number | null;
  videoModalOpen: boolean;
};

//Coach

export type Option = { value: number; label: string };

export type EachHighLightVideoItem = {
  id: number;
  title: string;
  video: string | null;
  aboutVideo: string;
};

export type ProfileGalleryItem = {
  id: number;
  title: string;
  image: string;
};

export type AvailabilitySlot = {
  id: string;
  fromTimeSlotsId: number | null;
  toTimeSlotsId: number | null;
  webLink: string;
  notes: string;
  isHidden: boolean;
};

export type CoachAvailabilityItem = {
  id: number;
  roleId: number;
  coachId: number;
  userId: number;
  usTimezonesId: number;
  dayVal: string;
  isAvailable: boolean;
  fromTimeSlotsId: number;
  toTimeSlotsId: number;
  webLink: string;
  notes: string;
  isHidden: boolean;
  createdAt: string;
  updatedAt: string;
  usTimezones: {
    id: number;
    timezoneName: string;
    abbreviation: string;
    utcOffsetStandard: string;
    utcOffsetDst: string;
    observesDst: boolean;
    createdAt: string;
    updatedAt: string;
  };
  fromTimeSlot: {
    id: number;
    time24hr: string;
    time12hr: string;
    createdAt: string;
    updatedAt: string;
  };
  toTimeSlot: {
    id: number;
    time24hr: string;
    time12hr: string;
    createdAt: string;
    updatedAt: string;
  };
};

export type AvailibilityItem = {
  id: string;
  day: string;
  isAvailable: boolean;
  startTime: string | Date;
  endTime: string | Date;
  slots: AvailabilitySlot[];
  apiSlots?: CoachAvailabilityItem[];
};

export type EachCertificateItem = {
  id: number;
  documentType: Option;
  title: string;
  uploadedOn?: Date | null | undefined;
  file: string | null;
  documentLink: string;
};

export type ResumeData = {
  title: string;
  link: string;
  resume: string | null;
};

export type ContactInfo = {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  lastTAndCAccepted?: string;
};

export type VerificationItem = {
  title: string;
  description?: string;
  documentLink?: string;
  documentType: { value: number; label: string } | undefined;
  otherType?: string;
  expirationDate: Date | undefined;
  file: string | null | undefined;
};

export type CoachProfileTypes = {
  loading: boolean;
  error: string;
  profileToggle: boolean;
  isProfileEditable: boolean;
  coachProfileData: any | null;
  email: string;
  selectedAgeGroups: Option[];
  selectedGender: Option[];
  toggleCoachingFocus: boolean;
  allCoachingFocusesList: any[];
  selectedFocuses: Option[];
  toggleCoachingBackground: boolean;
  allCoachingBackground: Option[];
  selectedBackgrounds: Option[];
  toggleAboutVideo: boolean;
  aboutVideoFile: null | string;
  toggleShortBio: boolean;
  isBioEditable: boolean;
  shortBio: string;
  toggleSocialMedia: boolean;
  coachSocialMediaList: EachSocialMediaItem[];
  toggleQuickLinks: true;
  coachQuickLinksList: EachQuickLinkItem[];
  toggleWebsite: boolean;
  website: string;
  toggleAffiliation: boolean;
  allAffiliationTypesList: Option[];
  selectedAffiliationType: Option | null;
  allCurrentAffiliations: Option[];
  selectedCurrentAffiliation: Option | null;
  currentAffiliation: string;
  allWhoAmIRightNowList: Option[];
  selectedWhoAmIRightNow: null | Option;
  otherProfession: string;
  selectedIState: Option | null;
  selectedILocations: Option[];
  toggleWhyIAm: boolean;
  allWhyIAmOptions: Option[];
  selectedWhyIAm: Option[];
  toggleMyCoachingFocus: boolean;
  allMyCoachingFocusList: Option[];
  selectedMyCoachingFocuses: Option[];
  toggleWhatIOfferAsCoach: boolean;
  allCoachOfferList: Option[];
  selectedCoachOffer: Option[];
  toggleSportInfoSection: boolean;
  coachSelectedSportsList: EachSportItem[];
  openVirtualSession: boolean;
  selectedState: EachSearchItem | null;
  selectedCounties: EachSearchItem[];
  selectedLocations: EachSearchItem[];
  coachAddedStateLocationsList: AddedStateLocationsItem[];
  toggleHighLightVideo: boolean;
  highLightVideoData: EachHighLightVideoItem | null;
  toggleGallery: boolean;
  galleryData: ProfileGalleryItem | null;
  galleriesList: ProfileGalleryItem[];
  allTimeZoneList: any[];
  toggleAvailability: boolean;
  availableTimeZone: { value: number; label: string } | null;
  allTimeSlotsList: { id: number; time12hr: string; time24hr: string }[];
  generalAvailabilityList: AvailibilityItem[];
  toBookTime: Date | undefined;
  toggleToBookTime: boolean;
  availabilityNote: string;
  toggleAvailabilityNote: boolean;
  toggleCertification: boolean;
  certificatesData: EachCertificateItem | null;
  isAddCertificates: boolean;
  coachAddedCertificatesList: EachCertificateItem[];
  isEditCertificate: boolean;
  toggleResumeSection: boolean;
  coachResumeData: ResumeData | null;
  addedResumeData: ResumeData | null;
  toggleContactInfo: boolean;
  coachContactInfo: ContactInfo;
  isEditContactInfo: boolean;
  togglePhone: boolean;
  toggleEmail: boolean;
  govtIdData: VerificationItem;
  addedGovtIdData: VerificationItem | null;
  additionalDocList: VerificationItem[];
  declarations: {
    accuracy: boolean;
    responsibility: boolean;
    ongoing: boolean;
    consent: boolean;
    agreeAll: boolean;
    eSign: string;
    date: Date | undefined;
  };
};

//Coach Sport

export type EachScoreCard = {
  id: number;
  statsAsOf: string;
  yearsCoaching: number;
  athletesTrained: number;
  toggleAthletesTrained: boolean;
  teamChampionshipWon: number;
  toggleChampionshipWon: boolean;
  athleteImprovementRate: number;
  toggleAthleteImprovementRate: boolean;
  athleteRetentionRate: number;
  toggleRetentionRate: boolean;
  certificationsEarned: number;
  toggleCertificationEarned: boolean;
  description: string;
  athletePlacements: {
    id: string;
    placement: string;
    athletesTrained: number;
    toggleHide: boolean;
  }[];
};

export type CoachSportStates = {
  error: string;
  loading: boolean;
  selectedSport: EachSearchItem | null;
  togglePrimary: boolean;
  addedSportLevelsList: Option[];
  addedSpecilitiesList: Option[];
  toggleTeamCoaching: boolean;
  currentTeamList: { id: number; team: string; isPrimary: boolean }[];
  toggleStrengths: boolean;
  addedUniqueStrengths: Option[];
  toggleStatsForm: boolean;
  statsFormData: {
    statsAsOf: Date | undefined;
    yearsCoaching: string;
    athletesTrained: string;
    toggleAthletesTrained: boolean;
    teamChampionshipWon: string;
    toggleChampionshipWon: boolean;
    athleteImprovementRate: string;
    toggleAthleteImprovementRate: boolean;
    athleteRetentionRate: string;
    toggleRetentionRate: boolean;
    certificationsEarned: string;
    toggleCertificationEarned: boolean;
    description: string;
  } | null;
  athletePlacementsList: {
    id: string;
    placement: string;
    athletesTrained: string;
    toggleHide: boolean;
  }[];
  scoreCardDate: Date | undefined;
  addedStatsScoreCardList: EachScoreCard[];
  scoreCardId: number | null;
  toggleTrackRecord: boolean;
  isAddTrackRecord: boolean;
  trackRecordItem: EachMileStoneVictoryItem | null;
  addedTrackRecordsList: EachMileStoneVictoryItem[];
  toggleVideoSection: boolean;
  highLightVideoData: EachHighLightVideoItem | null;
  addedHighLightVideosList: EachHighLightVideoItem[];
  toggleHighLightLinks: boolean;
  isEditHighlightLinks: boolean;
  highLightLinksList: { id: number; text: string; url: string }[];
  imagesGalleryList: { image: File }[];
};

//Premium
export type EachPremiumPlan = {
  id: number;
  subscriptionName: string;
  description: string;
  roleId: number;
  subscriptionAmount: number;
  subscriptionLimit: number;
  processingFee: number;
  convenienceFee: number;
  serviceFee: number;
  totalSubscriptions: number;
  duration_days: number;
  renewal_window_days: number;
  isEnabled: boolean;
  createdUser: number;
  updatedUser: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
};

export type EachBillingState = {
  id: number;
  roleId: number;
  stateId: number;
  stateName: string;
  tax: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  label?: string;
  value?: number;
};

export type PromoCode = {
  code: string;
  discountPrcnt: number;
  isVerified: boolean;
  status: string;
};

export type EachTransaction = {
  id: number;
  transactionId: string;
  userId: number;
  profileId: number;
  subscriptionPlanId: number;
  subscriptionPlanName: string;
  isLatest: boolean;
  subscriptionPlanDuration: number;
  renewalWindow: number;
  billingStateId: number;
  promoCode: string;
  discountPercentage: number;
  taxPercentage: number;
  subscriptionPlanAmount: number;
  discountAmount: number;
  netPlanAmount: number;
  taxAmount: number;
  processingFees: number;
  convenienceFees: number;
  serviceFees: number;
  totalAmountPaid: number;
  paymentDate: string;
  subscriptionStartDate: string;
  subscriptionEndDate: string;
  renewalDate: string;
  renewalWindowStartDate: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: null | string;
  roleId: number;
  createdUser: number;
  updatedUser: number;
  subscriptionTransactionId: null | string | number;
};

export type PremiumStateModal = {
  apiStatus: string;
  error: string;
  premiumPlansList: EachPremiumPlan[] | undefined;
  billingStatesList: EachBillingState[];
  selectedBillingState: EachBillingState | null;
  promoCode: PromoCode | null;
  planSummaryData: EachPremiumPlan | null;
  transactionsList: EachTransaction[] | undefined;
  userSubscriptionData: null | any;
};

export type PaymentData = {
  billingStateId: number;
  promoCode?: string;
  discountPercentage?: number;
  taxPercentage: number;
  subscriptionPlanAmount: number;
  discountAmount?: number;
  netPlanAmount?: number;
  taxAmount: number;
  processingFees?: number;
  convenienceFees?: number;
  serviceFees?: number;
  totalAmountPaid: number;
  [key: string]: any;
};

export type AthleteHomeStateModal = {
  generalAnnouncements: [];
  sportsAnnouncements: [];
  apiStatus: string;
};

// utils/interfaces.ts
export type CMSSection = {
  id: number;
  title: string;
  shortDescription: string;
  description: string | null;
  isPublish: boolean;
  cmId: number;
  createdAt: string;
  updatedAt: string;
};

export type GalleryItem = {
  id: number;
  fileLocation: string;
  fileType: string;
  fileTitle: string;
  fileDescription: string;
  cmId: number;
  cmssectionId: number;
  createdAt: string;
  updatedAt: string;
  fileTag: string;
  fileUrl: string
}

// ✅ Extended HomeCMSState with new fields
export type HomeCMSState = {
  loading: boolean;
  error: string;

  homeBannerImages: GalleryItem[];
  cmsSections: CMSSection[];
  cmsImages: GalleryItem[];

  unlockSection: CMSSection | null;
  unlockImages: GalleryItem[];

  howItWorksSection: CMSSection | null;
  howItWorksImages: GalleryItem[];

  testimonialSection: CMSSection | null;
  testimonialImages: GalleryItem[];

  mission1Section: CMSSection | null;
  mission1Images: GalleryItem[];

  mission2Section: CMSSection | null;
  mission2Images: GalleryItem[];

  athleteInActionSection: CMSSection | null;
  athleteInActionImages: GalleryItem[];

  athleteCommunitySection: CMSSection | null;
  athleteCommunityImages: GalleryItem[];

  youthEcosystemSection: CMSSection | null;
  youthEcosystemImages: GalleryItem[];

  ourSportsSection: CMSSection | null;
  ourSportsImages: GalleryItem[];

  aboutSection: CMSSection | null;
  aboutVideo: GalleryItem[];

  ourCoachesSection: CMSSection | null;
  ourCoachesImages: GalleryItem[];

  smartTechnologySection: CMSSection | null;
  smartTechnologyImages: GalleryItem[];

  mobileStoreSection: CMSSection | null;
  mobileStoreImages: GalleryItem[];

  builtForFamiliesSection: CMSSection | null;
  builtForFamiliesImages: GalleryItem[];

  homeAnnouncementSection: CMSSection | null;
  homeAnnouncementImages: GalleryItem[];

  ourImpactSection: CMSSection | null;
  ourImpactImages: GalleryItem[];

  connectAthleteEcoSystemSection: CMSSection | null;
  connectAthleteEcoSystemImages: GalleryItem[];
};

export type Coach = {
  id: number;
  name: string;
  sport: string;
  image: string;
  accomplishment: string;
  fullDescription: string;
};

interface ExposeSectionsState {
  toggles: {
    userId: number;
    profile: boolean;
    socialMedia: boolean;
    gallery: boolean;
    quickLinks: boolean;
    physicalStats: boolean;
    achievements: boolean;
    parentGuardian: boolean;
    allowCurrentSchoolToView: boolean;
  };
}
