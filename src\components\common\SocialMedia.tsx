'use client'
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch } from "@/store";
import { fetchAthleteSocialMediaLinks, postAthleteSocialMedia, fetchCoachSocialMediaLinks, postCoachSocialMedia, editCoachSocialMediaLinks } from "@/store/slices/coach/coachProfileSlice";
import { EachSocialMediaItem } from "@/utils/interfaces";
import { Loader, PencilLine } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { Skeleton } from "../ui/skeleton";
import UpgradePremiumSection from "./UpgradePremiumSection";

interface IProps {
    toggleSocialMediaSection: boolean;
    onChangeToggleSection: (checked: boolean) => void
    list: EachSocialMediaItem[];
    origin: string;
    loading?: boolean;
    fetchLoading?: boolean;
}

const SocialMedia = ({
    toggleSocialMediaSection,
    onChangeToggleSection,
    list,
    origin,
    loading,
    fetchLoading,
}: IProps) => {
    const [socialMediaList, setSocialMediaList] = useState<EachSocialMediaItem[]>(list)
    const [editableIndex, setEditableIndex] = useState<number | null>(null);
    const dispatch = useDispatch<AppDispatch>()
    const { userId, roleId, isPremiumUser } = useTokenValues()
    const { profileId } = useLocalStoredInfo()
    const router = useRouter()
    const isAthletePremiumUser = origin === 'athlete' && isPremiumUser

    useEffect(() => {
        list && setSocialMediaList(list)
    }, [list])

    const onClickPencil = (index: number | null) => {
        setEditableIndex(index);
    };

    const onChangeLink = (index: number, value: string) => {
        const updated = socialMediaList?.map((item, i) =>
            i === index ? { ...item, link: value } : item
        );
        setSocialMediaList(updated);
    };

    const onToggleEnable = (index: number, checked: boolean) => {
        const updated = [...socialMediaList];
        const item = { ...updated[index], isEnable: checked };
        updated[index] = item;
        setSocialMediaList(updated);
    };

    const apiSuccessStatus = async (api, action, fetchAPI) => {
        try {
            if (api.fulfilled.match(action)) {
                setEditableIndex(null);
                await dispatch((fetchAPI()))
                toast.success(`Social media links saved successfully!`);
            } else {
                toast.error('Failed to add. Please try again.');
            }
        } catch (error) {
            toast.error('An error occurred while updating.');
        }
    };

    const handleSaveSocialMedia = async () => {
        const filteredSocialMedia = socialMediaList?.filter(account => account?.link?.trim() !== "").map(account => ({
            socialMedia: account?.id,
            socialMediaLink: account?.link,
            isHidden: !account?.isEnable
        }));

        const payload = {
            roleId,
            userId,
            athleteId: profileId,
            socialMediaAccounts: filteredSocialMedia,
        };

        switch (origin) {
            case 'athlete':
                const socialMediaAction = await dispatch(postAthleteSocialMedia(payload));
                apiSuccessStatus(postAthleteSocialMedia, socialMediaAction, fetchAthleteSocialMediaLinks)
                break;
            case 'coach':
                const coachSocialMediaAction = await dispatch(postCoachSocialMedia(payload));
                apiSuccessStatus(postCoachSocialMedia, coachSocialMediaAction, fetchCoachSocialMediaLinks)
                break;
            case 'business':

                break;
            default:
                break;
        }

    };

    if (fetchLoading) {
        return (
            <div className="flex flex-col items-center gap-6">
                <Skeleton className="h-[20px] w-full rounded-lg " />
                <Skeleton className="h-[20px] w-full rounded-lg " />
                <Skeleton className="h-[20px] w-full rounded-lg " />
            </div>
        )
    }


    return (
        <div className="w-full flex flex-col gap-8 bg-slate-100 p-4 rounded-lg">
            {!isAthletePremiumUser && <UpgradePremiumSection />}
            <div className="flex items-center justify-center gap-4">
                <h3 className="font-bold text-xl text-center">Social Media</h3>
                <Switch
                    name="toggleSocialMediaSection"
                    checked={toggleSocialMediaSection}
                    onCheckedChange={onChangeToggleSection}
                    disabled={!isAthletePremiumUser}
                />
            </div>

            {toggleSocialMediaSection && (
                <div className="flex flex-col justify-between gap-4 w-full">
                    {socialMediaList?.map((item, index) => (
                        <div key={item?.id} className="w-full flex items-center gap-3">
                            {/* Icon */}
                            <img src={item?.icon} alt={item?.id} className="h-8 w-8 shrink-0" loading="lazy" />

                            {/* Link/Input with Pencil */}
                            <div className="flex-1 flex items-center gap-1 bg-slate-200 rounded-xl hover:shadow-md  py-1 min-w-0">
                                <div className="flex-1 min-w-0">
                                    {editableIndex === index ? (
                                        <Input
                                            value={item?.link}
                                            onChange={(e) => onChangeLink(index, e.target.value)}
                                            className="w-full border-slate-500"
                                            placeholder={`Enter ${item?.id} URL`}
                                            onBlur={() => onClickPencil(null)}
                                            disabled={!isAthletePremiumUser}
                                        />
                                    ) : (
                                        <a
                                            href={item?.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="block pl-3 w-full text-blue-900 hover:underline truncate"
                                            title={item?.link}
                                        >
                                            {item?.link}
                                        </a>
                                    )}
                                </div>

                                {editableIndex === index ? null : (
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => onClickPencil(index)}
                                        className="shrink-0"
                                        disabled={!isAthletePremiumUser}
                                    >
                                        <PencilLine className="w-4 h-4" />
                                    </Button>
                                )}
                            </div>

                            {/* Toggle switch */}
                            <Switch
                                checked={item?.isEnable}
                                onCheckedChange={(checked) => onToggleEnable(index, checked)}
                                className="shrink-0"
                                disabled={!isAthletePremiumUser}
                            />
                        </div>
                    ))}

                    <div className="flex justify-end items-end">
                        <Button className="w-24" onClick={handleSaveSocialMedia} disabled={!isAthletePremiumUser}>
                            {loading ? (
                                <Loader className="animate-spin text-white w-10 h-10" />
                            ) : (
                                'Save'
                            )}
                        </Button>
                    </div>
                </div>
            )}
        </div>

    )
}
export default SocialMedia;

