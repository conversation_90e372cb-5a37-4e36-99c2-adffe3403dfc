'use client'
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { PencilLine, Check, X } from 'lucide-react';

const TestEditDebugPage = () => {
  const [editableIndex, setEditableIndex] = useState<number | null>(null);
  const [editingItem, setEditingItem] = useState<{link: string, isHidden: boolean} | null>(null);

  // Mock social media data
  const [socialMediaList, setSocialMediaList] = useState([
    {
      id: "instagram",
      icon: "/instagram.svg",
      link: "https://instagram.com/test",
      isEnable: true,
    },
    {
      id: "x",
      icon: "/X.jpeg",
      link: "",
      isEnable: true,
    },
    {
      id: "facebook",
      icon: "/facebook.svg",
      link: "https://facebook.com/test",
      isEnable: false,
    }
  ]);

  const onClickEdit = (index: number) => {
    console.log('Edit clicked for index:', index);
    const item = socialMediaList[index];
    console.log('Item to edit:', item);
    
    setEditableIndex(index);
    setEditingItem({
      link: item.link,
      isHidden: !item.isEnable
    });
    
    console.log('Edit state set - editableIndex:', index);
  };

  const onClickCancel = () => {
    console.log('Cancel clicked');
    setEditableIndex(null);
    setEditingItem(null);
  };

  const onChangeLinkValue = (value: string) => {
    console.log('Link value changed:', value);
    if (editingItem) {
      setEditingItem({
        ...editingItem,
        link: value
      });
    }
  };

  const onToggleVisibility = (checked: boolean) => {
    console.log('Visibility toggled:', checked);
    if (editingItem) {
      setEditingItem({
        ...editingItem,
        isHidden: !checked
      });
    }
  };

  const handleSave = () => {
    console.log('Save clicked');
    if (editableIndex !== null && editingItem) {
      // Update the local state
      const updatedList = [...socialMediaList];
      updatedList[editableIndex] = {
        ...updatedList[editableIndex],
        link: editingItem.link,
        isEnable: !editingItem.isHidden
      };
      setSocialMediaList(updatedList);
      
      // Reset editing state
      setEditableIndex(null);
      setEditingItem(null);
      
      console.log('Item saved:', updatedList[editableIndex]);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Edit Debug Test Page</h1>
      
      <div className="mb-4 p-4 bg-blue-50 rounded-lg">
        <h2 className="font-semibold mb-2">Debug Info</h2>
        <p><strong>Editable Index:</strong> {editableIndex !== null ? editableIndex : 'None'}</p>
        <p><strong>Editing Item:</strong> {editingItem ? JSON.stringify(editingItem) : 'None'}</p>
      </div>

      <div className="w-full flex flex-col gap-4 bg-slate-100 p-4 rounded-lg">
        <h3 className="font-bold text-xl text-center">Social Media Test</h3>
        
        <div className="flex flex-col gap-4">
          {socialMediaList.map((item, index) => (
            <div key={item.id} className="w-full flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm">
              {/* Icon */}
              <img src={item.icon} alt={item.id} className="h-8 w-8 shrink-0" loading="lazy" />

              {/* Content */}
              <div className="flex-1 flex flex-col gap-2 min-w-0">
                {editableIndex === index ? (
                  <>
                    {/* Edit Mode */}
                    <div className="flex items-center gap-2">
                      <Input
                        value={editingItem?.link || ''}
                        onChange={(e) => onChangeLinkValue(e.target.value)}
                        className="flex-1 border-slate-500"
                        placeholder={`Enter ${item.id} URL`}
                      />
                    </div>
                    
                    {/* Visibility Toggle */}
                    <div className="flex items-center gap-2 text-sm">
                      <span>Visible:</span>
                      <Switch
                        checked={!editingItem?.isHidden}
                        onCheckedChange={onToggleVisibility}
                        size="sm"
                      />
                      <span className="text-gray-500">
                        {editingItem?.isHidden ? 'Hidden' : 'Visible'}
                      </span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={handleSave}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Check className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={onClickCancel}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    {/* Display Mode */}
                    <div className="flex items-center gap-2">
                      <div className="flex-1 min-w-0">
                        {item.link ? (
                          <a
                            href={item.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block text-blue-900 hover:underline truncate"
                            title={item.link}
                          >
                            {item.link}
                          </a>
                        ) : (
                          <span className="text-gray-400 italic">No link added</span>
                        )}
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onClickEdit(index)}
                      >
                        <PencilLine className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* Status Display */}
                    <div className="flex items-center gap-2 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        item.isEnable 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {item.isEnable ? 'Visible' : 'Hidden'}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Current State Display */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Current State</h2>
        <pre className="text-sm overflow-auto">
          {JSON.stringify(socialMediaList, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default TestEditDebugPage;
