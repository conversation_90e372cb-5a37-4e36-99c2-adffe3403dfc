
import axiosInstance from "@/utils/axiosInstance";
import { CoachProfileTypes } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";

// Interface for Coach Verification payload
interface CoachVerificationPayload {
  coachESign: string;
  eSignDateTime: string;
  verifyStartDt: string;
  verifyNextStartDate: string;
  documentAccuracyAuthenticity: boolean;
  responsibilityDocumentExpiration: boolean;
  ongoingVerificationTerms: boolean;
  consentDisplayVerifiedBadge: boolean;
}

const initialState: CoachProfileTypes = {
  loading: false,
  error: "",
  profileToggle: false,
  coachProfileData: null,
  isProfileEditable: false,
  selectedAgeGroups: [],
  selectedGender: [],
  toggleCoachingFocus: true,
  allCoachingFocusesList: [],
  selectedFocuses: [],
  toggleCoachingBackground: true,
  allCoachingBackground: [],
  selectedBackgrounds: [],
  toggleAboutVideo: true,
  aboutVideoFile: null,
  email: "",
  isBioEditable: false,
  toggleShortBio: true,
  shortBio: "",
  toggleSocialMedia: true,
  coachSocialMediaList: [
    {
      id: "x",
      icon: "/X.jpeg",
      link: "x.com",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "instagram",
      icon: "/instagram.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "facebook",
      icon: "/facebook.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "youtube",
      icon: "/youtube.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "linkedin",
      icon: "/linkedin.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
  ],
  toggleQuickLinks: true,
  coachQuickLinksList: [],
  toggleWebsite: true,
  website: "",
  toggleAffiliation: true,
  allAffiliationTypesList: [],
  selectedAffiliationType: null,
  allCurrentAffiliations: [],
  selectedCurrentAffiliation: null,
  currentAffiliation: "",
  allWhoAmIRightNowList: [
    { value: 1, label: "General" },
    { value: 2, label: "Other" },
  ],
  selectedWhoAmIRightNow: null,
  otherProfession: "",
  selectedIState: null,
  selectedILocations: [],
  toggleWhyIAm: true,
  allWhyIAmOptions: [],
  selectedWhyIAm: [],
  toggleMyCoachingFocus: true,
  allMyCoachingFocusList: [
    { value: 1, label: "Offering-private-training" },
    { value: 2, label: "Cpromoting-camps-clinics" },
    { value: 3, label: "Recruiting Athletes" },
    { value: 4, label: "Mentoring-youth" },
  ],
  selectedMyCoachingFocuses: [],
  toggleWhatIOfferAsCoach: true,
  allCoachOfferList: [
    { value: 1, label: "College-recruiting-preparation" },
    { value: 2, label: "Individual-skill-development" },
    { value: 3, label: "Tryout-preparation" },
    { value: 4, label: "Positive-mindset-coaching" },
  ],
  selectedCoachOffer: [],
  toggleSportInfoSection: true,
  coachSelectedSportsList: [ ],
  openVirtualSession: true,
  selectedState: null,
  selectedCounties: [],
  selectedLocations: [],
  coachAddedStateLocationsList: [],
  toggleHighLightVideo: true,
  highLightVideoData: null,
  toggleGallery: true,
  galleryData: null,
  galleriesList: [],
  toggleAvailability: true,
  allTimeZoneList: [
    { value: 1, label: "EST" },
    { value: 2, label: "IST" },
  ],
  availableTimeZone: null,
  allTimeSlotsList: [],
  generalAvailabilityList: [
    {
      id: "monday",
      day: "Monday",
      isAvailable: false,
      startTime: "",
      endTime: "",
      slots: [],
      apiSlots: [],
    },
    {
      id: "tuesday",
      day: "Tuesday",
      isAvailable: false,
      startTime: "",
      endTime: "",
      slots: [],
      apiSlots: [],
    },
    {
      id: "wednesday",
      day: "Wednesday",
      isAvailable: false,
      startTime: "",
      endTime: "",
      slots: [],
      apiSlots: [],
    },
    {
      id: "thursday",
      day: "Thursday",
      isAvailable: false,
      startTime: "",
      endTime: "",
      slots: [],
      apiSlots: [],
    },
    {
      id: "friday",
      day: "Friday",
      isAvailable: false,
      startTime: "",
      endTime: "",
      slots: [],
      apiSlots: [],
    },
    {
      id: "saturday",
      day: "Saturday",
      isAvailable: false,
      startTime: "",
      endTime: "",
      slots: [],
      apiSlots: [],
    },
    {
      id: "sunday",
      day: "Sunday",
      isAvailable: false,
      startTime: "",
      endTime: "",
      slots: [],
      apiSlots: [],
    },
  ],
  toBookTime: undefined,
  toggleToBookTime: true,
  availabilityNote: "",
  toggleAvailabilityNote: true,
  toggleCertification: true,
  certificatesData: null,
  isAddCertificates: false,
  coachAddedCertificatesList: [],
  isEditCertificate: false,
  toggleResumeSection: true,
  coachResumeData: null,
  addedResumeData: null,
  toggleContactInfo: true,
  coachContactInfo: {
    firstName: "",
    lastName: "",
    phone: "",
    email: "",
    lastTAndCAccepted: "",
  },
  isEditContactInfo: false,
  togglePhone: true,
  toggleEmail: true,
  govtIdData: {
    title: "",
    description: "",
    documentLink: "",
    documentType: undefined,
    otherType: "",
    expirationDate: undefined,
    file: null,
  },
  addedGovtIdData: null,
  additionalDocList: [
    {
      title: "",
      description: "",
      documentLink: "",
      documentType: undefined,
      otherType: "",
      expirationDate: undefined,
      file: null,
    },
    {
      title: "",
      description: "",
      documentLink: "",
      documentType: undefined,
      otherType: "",
      expirationDate: undefined,
      file: null,
    },
    {
      title: "",
      description: "",
      documentLink: "",
      documentType: undefined,
      otherType: "",
      expirationDate: undefined,
      file: null,
    },
  ],
  declarations: {
    accuracy: false,
    responsibility: false,
    ongoing: false,
    consent: false,
    agreeAll: false,
    eSign: "",
    date: undefined,
  },
};


export const fetchCoachProfile = createAsyncThunk(
  "coachIntro/fetchCoachProfile",
  async (_, { fulfillWithValue, rejectWithValue }) => {

    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-profile/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }


  }
);

export const editCoachProfile = createAsyncThunk(
  "coachIntro/editCoachProfile",
  async (updatedprofileData: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-profile/${userId}`,
        updatedprofileData,
      );

      if (response.status === 200 || response.status === 201) {
        toast.success("Profile updated successfully!");
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }


  }
);

export const getCoachFocusAll = createAsyncThunk(
  "coachFocus/getCoachFocusAll",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/getcoachfocus`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachFocusById = createAsyncThunk(
  "coachFocus/getCoachFocusId",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-focus?userId=${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachBackgroundAll = createAsyncThunk(
  "coachFocus/getCoachBackgroundAll",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/getcoachbackgrounds`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachPlatformTags = createAsyncThunk(
  "coachFocus/getplatformtags",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/getplatformtags`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachOfferTags = createAsyncThunk(
  "coachFocus/getoffertags",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/getoffertags`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachSelectedBackgrounds = createAsyncThunk(
  "coachFocus/getCoachSelectedBackgrounds",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-background?userId=${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachAffiliationType = createAsyncThunk(
  "coachFocus/getCoachAffiliationType",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/getcoachaffiliations`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachTimeZonesType = createAsyncThunk(
  "coachFocus/getCoachTimezonesType",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/gettimezones`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachTimeslots = createAsyncThunk(
  "coachFocus/getCoachTimeslots",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/gettimeslots`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const editCoachFocus = createAsyncThunk(
  "coachFocus/getEditCoachFocus",
  async (updateCoachFocus: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-focus/${userId}`,
         updateCoachFocus,
      );

      if (response.status === 200 || response.status === 201) {
        toast.success("Coach Focus updated successfully!");
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const editCoachBackground = createAsyncThunk(
  "coachFocus/getEditCoachBackground",
  async (updateCoachBackgrnd: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-background/${userId}`,
         updateCoachBackgrnd,
      );

      if (response.status === 200 || response.status === 201) {
        toast.success("Coach Background updated successfully!");
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachWhyonCA = createAsyncThunk(
  "coachFocus/coachwhyonca",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-why-on-ca?userId=${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const getCoachOffer = createAsyncThunk(
  "coachFocus/getCoachOffer",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-offers?userId=${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const editCoachOffer = createAsyncThunk(
  "coachFocus/editCoachOffer",
  async (updateCoachOffer: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-offers/${userId}`,
         updateCoachOffer,
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const editCoachWhyonCA = createAsyncThunk(
  "coachFocus/editCoachWhyonCA",
  async (updateCoachOffer: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-why-on-ca/${userId}`,
         updateCoachOffer,
      );

      if (response.status === 200 || response.status === 201) {
        toast.success("Why I am on Connect Athlete updated successfully!");
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const postCoachSocialMedia = createAsyncThunk(
  "coachSocialMedia/postCoachSocialMedia",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-social-media/${payload.userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);

export const fetchCoachSocialMediaLinks = createAsyncThunk(
  "coachSocialMedia/fetchCoachSocialMediaLinks",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-social-media?userId=${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const editCoachSocialMediaLinks = createAsyncThunk(
  "coachSocialMedia/editCoachSocialMediaLinks",
  async (payload: { roleId: number; coachId: number; userId: number; socialMedia: string; socialMediaLink: string; isHidden: boolean }[], { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId"); 
    try {                     
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-social-media/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {   
        return fulfillWithValue(response.data.data || response.data);
      }       else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);  

export const fetchCoachVerification = createAsyncThunk(
  "coachSocialMedia/fetchCoachVerification",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-verification/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const postCoachVerification = createAsyncThunk(
  "coachSocialMedia/postCoachVerification",
  async (payload: CoachVerificationPayload, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-verification`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);


export const fetchCoachQuickLinks = createAsyncThunk(
  "coachQuickLinks/fetchCoachQuickLinks",
  async (_, { fulfillWithValue, rejectWithValue }) => {  

    const userId = localStorage.getItem("userId");
    try {                     
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-quick-links?userId=${userId}`
      );

      if (response.status === 200 || response.status === 201) {   
        return fulfillWithValue(response.data.data || response.data);
      }       else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);


export const postCoachQuickLinks = createAsyncThunk(
  "coachQuickLinks/postCoachQuickLinks",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {  
    try {                     
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-quick-links`,
        payload
      );

      if (response.status === 200 || response.status === 201) {   
        return fulfillWithValue(response.data.data || response.data);
      }       else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);

export const putCoachQuickLinks = createAsyncThunk(
  "coachQuickLinks/putCoachQuickLinks",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {  
    const userId = localStorage.getItem("userId");
    try {                     
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-quick-links/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {   
        return fulfillWithValue(response.data.data || response.data);
      }       else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);

export const deleteCoachQuickLinks = createAsyncThunk(
  "coachQuickLinks/deleteCoachQuickLinks",
  async (id: any, { fulfillWithValue, rejectWithValue }) => {  
    try {                     
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-quick-links/${id}`,
      );

      if (response.status === 200 || response.status === 201) {   
        return fulfillWithValue(response.data.data || response.data);
      }       else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);

export const fetchCoachGallery = createAsyncThunk(
  "coachGallery/fetchCoachGallery",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athletegallery`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const deleteCoachGalleryItem = createAsyncThunk(
  "coachGallery/deleteAthleteGalleryItem",
  async (galleryId: number, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_V2_CONTENT_API_URL}/delete-file/${galleryId}`
      );

      if (response.status === 200 || response.status === 201) {
                toast.success("Image Deleted successfully!");

        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchCoachVideoHighlight = createAsyncThunk(
  "coachGallery/fetchCoachVideoHighlight",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-video-highlight/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const postCoachVideoHighlight = createAsyncThunk(
  "coachGallery/postCoachVideoHighlight",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-video-highlight`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const postCoachAvailability = createAsyncThunk(
  "coachAvailability/postCoachAvailability",
  async (payload: any[], { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-general-availability`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);


export const fetchCoachAvailability = createAsyncThunk(
  "coachAvailability/fetchCoachAvailability",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-general-availability?userId=${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);

export const editCoachAvailability = createAsyncThunk(
  "coachAvailability/editCoachAvailability",  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    
    try {
      const response = await axiosInstance.patch(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-general-availability/${payload.id}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);

export const deleteCoachAvailability = createAsyncThunk(
  "coachAvailability/deleteCoachAvailability",
  async (id: any, { fulfillWithValue, rejectWithValue }) => {  
    try {                     
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-general-availability/${id}`,
      );

      if (response.status === 200 || response.status === 201) {   
        return fulfillWithValue(response.data.data || response.data);
      }       else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);

export const postAddSport = createAsyncThunk(
  "coachAvailability/postAddSport",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-sports`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);

export const fetchCoachSports = createAsyncThunk(
  "coachSports/fetchCoachSports",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-sports?userId=${userId}`
      );

      if (response.status === 200 || response.status === 201) { 
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {    
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);


  // "userId": 433,
  // "roleId": 2,
  // "coachId": 403,
  // "docTypeId": 6,
  // "docTypTxt": "Resume",
  // "docTitle": "My Professional Resume",
  // "docExpirationDate": "2025-12-31T00:00:00.000Z",
  // "docDesc": "Professional coaching resume with certifications",
  // "docLink": "https://example.com/resume.pdf",
  // "docFilePath": "/documents/resume.pdf",
  // "isHidden": false

  export const postAddGovtDocument = createAsyncThunk(
  "coachAvailability/postAddGovtDocument",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-documents`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);




const coachProfileSlice = createSlice({
  name: "coachProfile",
  initialState,
  reducers: {
    handleCoachInputChange: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCoachProfile.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCoachProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.coachProfileData = action.payload;
        state.shortBio = action.payload?.coachIntroBio || "";
        state.website = action.payload?.websiteLink || "";
        state.aboutVideoFile = action.payload?.coachIntroVideo || null;
        state.email = action.payload?.user?.email || "";
      })
      .addCase(fetchCoachProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
    builder
      .addCase(getCoachFocusAll.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachFocusAll.fulfilled, (state, action) => {
        state.loading = false;
        state.allCoachingFocusesList = action?.payload?.data?.map((each) => ({
          value: each?.id,
          label: each?.focusTagValue,
        }));;
      })
      .addCase(getCoachFocusAll.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
    builder
      .addCase(getCoachFocusById.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachFocusById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedFocuses = action?.payload?.map((each) => ({
          value: each?.couchfocus.id,
          label: each?.couchfocus.focusTagValue,
        }));;
      })
      .addCase(getCoachFocusById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
    builder
      .addCase(getCoachBackgroundAll.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachBackgroundAll.fulfilled, (state, action) => {
        state.loading = false;
        state.allCoachingBackground = action?.payload?.data?.map((each) => ({
          value: each?.id,
          label: each?.coachBackground,
        }));;
      })
      .addCase(getCoachBackgroundAll.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
    builder
      .addCase(getCoachSelectedBackgrounds.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachSelectedBackgrounds.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedBackgrounds = action?.payload?.map((each) => ({
          value: each?.backgroundLookup?.id,
          label: each?.backgroundLookup?.coachBackground,
        }));;
      })
      .addCase(getCoachSelectedBackgrounds.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
    builder
      .addCase(getCoachAffiliationType.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachAffiliationType.fulfilled, (state, action) => {
        state.loading = false;
        state.allAffiliationTypesList = action?.payload?.data?.map((each) => ({
          value: each?.id,
          label: each?.affiliationTypeValue,
        }));;
      })
      .addCase(getCoachAffiliationType.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
    builder
      .addCase(getCoachTimeZonesType.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachTimeZonesType.fulfilled, (state, action) => {
        state.loading = false;
        state.allTimeZoneList = action?.payload?.data?.map((each) => ({
          value: each?.id,
          label: each?.timezoneName,
        }));;
      })
      .addCase(getCoachTimeZonesType.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
      builder
      .addCase(getCoachTimeslots.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachTimeslots.fulfilled, (state, action) => {
        state.loading = false;
        state.allTimeSlotsList = action?.payload?.data?.map((each: any) => ({
          id: each?.id,
          time12hr: each?.time12hr,
          time24hr: each?.time24hr,
        }));
      })
      .addCase(getCoachTimeslots.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
    builder
      .addCase(getCoachPlatformTags.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachPlatformTags.fulfilled, (state, action) => {
        state.loading = false;
        state.allWhyIAmOptions = action?.payload?.data?.map((each) => ({
          value: each?.id,
          label: each?.pltfrmTagValue,
        }));;
      })
      .addCase(getCoachPlatformTags.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
    builder
      .addCase(getCoachOfferTags.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachOfferTags.fulfilled, (state, action) => {
        state.loading = false;
        state.allCoachOfferList = action?.payload?.data?.map((each) => ({
          value: each?.id,
          label: each?.offerTagValue,
        }));;
      })
      .addCase(getCoachOfferTags.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
      builder
      .addCase(getCoachWhyonCA.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachWhyonCA.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedWhyIAm = action?.payload?.map((each) => ({
          value: each?.coachPltfrmTag.id,
          label: each?.coachPltfrmTag.pltfrmTagValue,
        }));;
      })
      .addCase(getCoachWhyonCA.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
      builder
      .addCase(getCoachOffer.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachOffer.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedCoachOffer = action?.payload?.map((each) => ({
          value: each?.offerTag?.id,
          label: each?.offerTag?.offerTagValue,
        }));;
      })
      .addCase(getCoachOffer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
      builder
      .addCase(postCoachSocialMedia.pending, (state) => {
        state.loading = true;
      })
      .addCase(postCoachSocialMedia.fulfilled, (state) => {
        state.loading = false;
        toast.success("Social media link saved successfully!");
      })
      .addCase(postCoachSocialMedia.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to save social media");
      })
      .addCase(fetchCoachSocialMediaLinks.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCoachSocialMediaLinks.fulfilled, (state, action) => {
        state.loading = false;

        // Create a map of API data for easy lookup
        const apiDataMap = new Map();
        action.payload?.forEach((item: any) => {
          apiDataMap.set(item.socialMedia.toLowerCase(), {
            link: item.socialMediaLink,
            isHidden: item.isHidden,
          });
        });

        // Update the existing coachSocialMediaList with API data
        state.coachSocialMediaList = state.coachSocialMediaList.map((item) => {
          const apiData = apiDataMap.get(item.id.toLowerCase());
          return {
            ...item,
            link: apiData?.link || item.link,
            isEnable: apiData ? !apiData.isHidden : item.isEnable,
          };
        });
      })
      .addCase(fetchCoachSocialMediaLinks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(editCoachSocialMediaLinks.pending, (state) => {
        state.loading = true;
      })
      .addCase(editCoachSocialMediaLinks.fulfilled, (state) => {
        state.loading = false;
        toast.success("Social media link updated successfully!");
      })
      .addCase(editCoachSocialMediaLinks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to update social media link");
      });

      builder
      .addCase(fetchCoachVerification.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCoachVerification.fulfilled, (state, action) => {
        state.loading = false;
        state.declarations = action.payload;
      })
      .addCase(fetchCoachVerification.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
      builder
      .addCase(fetchCoachQuickLinks.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCoachQuickLinks.fulfilled, (state, action) => {
        state.loading = false;
        state.coachQuickLinksList = action.payload;
      })
      .addCase(fetchCoachQuickLinks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

      builder
      .addCase(postCoachQuickLinks.pending, (state) => {
        state.loading = true;
      })
      .addCase(postCoachQuickLinks.fulfilled, (state, action) => {
        state.loading = false;
        // Optionally add the new quick link to the list or refetch
        toast.success("Quick link added successfully!");
      })
      .addCase(postCoachQuickLinks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to add quick link");
      });

      builder
      .addCase(putCoachQuickLinks.pending, (state) => {
        state.loading = true;
      })
      .addCase(putCoachQuickLinks.fulfilled, (state, action) => {
        state.loading = false;
        toast.success("Quick link updated successfully!");
      })
      .addCase(putCoachQuickLinks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to update quick link");
      });
      builder
      .addCase(deleteCoachQuickLinks.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteCoachQuickLinks.fulfilled, (state, action) => {
        state.loading = false;
        toast.success("Quick link deleted successfully!");
      })
      .addCase(deleteCoachQuickLinks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to delete quick link");
      });
      builder
      .addCase(fetchCoachGallery.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCoachGallery.fulfilled, (state, action) => {
        state.loading = false;
        state.galleriesList = action.payload;
        // toast.success("Image added successfully!");
      })
      .addCase(fetchCoachGallery.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to fetch gallery");
      });
      builder
      .addCase(fetchCoachVideoHighlight.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCoachVideoHighlight.fulfilled, (state, action) => {
        state.loading = false;
        // Transform API response to match EachHighLightVideoItem interface
        if (action.payload) {
          state.highLightVideoData = {
            id: action.payload.id || Date.now(),
            title: action.payload.videoTitle || action.payload.title || "",
            video: action.payload.videoS3Path || action.payload.video || null,
            aboutVideo: action.payload.abtVideo || action.payload.aboutVideo || "",
          };
        }
      })
      .addCase(fetchCoachVideoHighlight.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to fetch video highlight");
      });

      builder
      .addCase(postCoachVideoHighlight.pending, (state) => {
        state.loading = true;
      })
      .addCase(postCoachVideoHighlight.fulfilled, (state, action) => {
        state.loading = false;
        // Transform API response to match EachHighLightVideoItem interface
        if (action.payload) {
          state.highLightVideoData = {
            id: action.payload.id || Date.now(),
            title: action.payload.videoTitle || action.payload.title || "",
            video: action.payload.videoS3Path || action.payload.video || null,
            aboutVideo: action.payload.abtVideo || action.payload.aboutVideo || "",
          };
        }
        toast.success("Video highlight saved successfully!");
      })
      .addCase(postCoachVideoHighlight.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to save video highlight");
      });

      builder
      .addCase(fetchCoachAvailability.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCoachAvailability.fulfilled, (state, action) => {
        state.loading = false;
        // Transform API response to match the UI structure
        const apiData = action.payload || [];
        const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

        state.generalAvailabilityList = daysOfWeek.map(day => {
          const daySlots = apiData.filter((item: any) => item.dayVal === day);
          return {
            id: day.toLowerCase(),
            day: day,
            isAvailable: daySlots.length > 0,
            startTime: "",
            endTime: "",
            slots: [],
            apiSlots: daySlots
          };
        });
      })
      .addCase(fetchCoachAvailability.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to fetch availability");
      });

      builder
      .addCase(postCoachAvailability.pending, (state) => {
        state.loading = true;
      })
      .addCase(postCoachAvailability.fulfilled, (state, action) => {
        state.loading = false;
        toast.success("Availability saved successfully!");
      })
      .addCase(postCoachAvailability.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to save availability");
      });

      builder
      .addCase(postAddSport.pending, (state) => {
        state.loading = true;
      })
      .addCase(postAddSport.fulfilled, (state, action) => {
        state.loading = false;
        toast.success("Sport added successfully!");
      })
      .addCase(postAddSport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to add sport");
      });
      builder
      .addCase(fetchCoachSports.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCoachSports.fulfilled, (state, action) => {
        console.log(action.payload, "action payload sports");
        state.loading = false;
        if (action.payload?.length > 0) {
          const formattedList = action.payload?.map((each) => ({
            id: each?.id,
            isPrimary: each?.primarySportFlag === "Y",
            sportName: each?.sport?.sportName,
            sportId: each?.sportsId,
            sportLevel: each?.levels?.length
              ? each?.levels?.map((levelsSport) => ({
                  id: levelsSport?.levelId,
                  value: levelsSport?.skillLevel?.levelValue,
                }))
              : [],
            specilities: each?.specialities?.length
              ? each?.specialities?.map((specialitySport) => ({
                  id: specialitySport?.specialityId,
                  specilityName: specialitySport?.speciality?.specialityTitle,
                }))
              : [],
          sportsProfileUrl: each?.sportsProfileUrl,     
            isEditable: false,
          }));
          state.coachSelectedSportsList = formattedList;
        }
      })
      .addCase(fetchCoachSports.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error("Failed to fetch sports");
      });

      // builder
      // .addCase(deleteCoachSport.pending, (state) => {
      //   state.loading = true;
      // })
      // .addCase(deleteCoachSport.fulfilled, (state, action) => {
      //   state.loading = false;
      //   toast.success("Sport deleted successfully!");
      // })
      // .addCase(deleteCoachSport.rejected, (state, action) => {
      //   state.loading = false;
      //   state.error = action.payload as string;
      //             toast.error("Failed to delete sport");
      // });
  }
});

export const { handleCoachInputChange } = coachProfileSlice.actions;
export default coachProfileSlice.reducer;

// // Example usage:
// const verificationData = {
//   coachESign: "John Does",
//   eSignDateTime: "2024-01-01T12:00:00.000Z",
//   verifyStartDt: "2024-01-01T00:00:00.000Z",
//   verifyNextStartDate: "2025-01-01T00:00:00.000Z",
//   documentAccuracyAuthenticity: true,
//   responsibilityDocumentExpiration: true,
//   ongoingVerificationTerms: true,
//   consentDisplayVerifiedBadge: true,
// };

// dispatch(postCoachVerification(verificationData));
